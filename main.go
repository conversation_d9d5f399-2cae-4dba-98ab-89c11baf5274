package main

import (
	"embed"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"

	"os"
)

//go:embed all:frontend/dist
var assets embed.FS

const uninstallArg = "--uninstall"

//go:embed extras/**
var extras embed.FS

var uninstallFlag bool

func main() {
	if len(os.Args) > 1 && os.Args[1] == uninstallArg {
		// backend.UninstallApp()
		// return
		uninstallFlag = true
	}
	// Create an instance of the app structure
	app := NewApp()
	// data, _ := extras.ReadFile("extras/test.txt")
	// tmpPath := "./tool.txt"
	// os.WriteFile(tmpPath, data, 0755)

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "myproject",
		Width:  1024,
		Height: 768,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		Frameless:        true,
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}
