{"version": "2.0.0", "tasks": [{"label": "wails:dev:build", "type": "shell", "command": "wails", "args": ["build", "-debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "wails:dev", "type": "shell", "command": "wails", "args": ["dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "isBackground": true}, {"label": "wails:build", "type": "shell", "command": "wails", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}