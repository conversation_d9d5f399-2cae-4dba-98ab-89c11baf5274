{"version": "0.2.0", "configurations": [{"name": "Wails Dev Debug", "type": "go", "request": "launch", "mode": "exec", "program": "${workspaceFolder}/build/bin/wails-test.exe", "preLaunchTask": "wails:dev:build", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "1"}, "showLog": true, "logOutput": "rpc"}, {"name": "Wails Debug (Direct Go)", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/main.go", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "1"}, "args": [], "showLog": true, "logOutput": "rpc"}, {"name": "Wails Debug with Uninstall Flag", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/main.go", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "1"}, "args": ["--uninstall"], "showLog": true, "logOutput": "rpc"}]}