package backend

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func UninstallApp() (string, error) {
	exePath, _ := os.Executable()

	batPath := filepath.Join(os.TempDir(), "delete_me.bat")
	batContent := fmt.Sprintf(`@echo off
timeout /t 1 /nobreak
rmdir /s /q "%s"
del "%s"`, InstallDir, exePath)
	if err := os.WriteFile(batPath, []byte(batContent), 0644); err != nil {
		return "", err
	}

	if err := exec.Command("cmd", "/C", "start", "", batPath).Start(); err != nil {
		return "", err
	}

	RemoveUninstallInfo(AppName)

	return "卸载已启动，安装目录及安装器将在几秒后删除", nil
}
