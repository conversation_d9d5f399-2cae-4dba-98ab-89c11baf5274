package main

import (
	"context"
	"fmt"

	Background "installer/backend"
)

// App struct
type App struct {
	ctx context.Context
}

// NewA<PERSON> creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// G<PERSON> returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func (a *App) Test() string {
	return "test"
}

func (a *App) Uninstall() {
	Background.UninstallApp()
}

func (a *App) Install() {
	Background.InstallApp()
}
